import firebase_admin
from firebase_admin import credentials, firestore, auth
from django.conf import settings
import json


class FirebaseService:
    """خدمة Firebase للمصادقة وقاعدة البيانات"""
    
    def __init__(self):
        if not firebase_admin._apps:
            # تكوين Firebase
            cred_dict = settings.FIREBASE_CONFIG
            if cred_dict.get('project_id'):
                cred = credentials.Certificate(cred_dict)
                firebase_admin.initialize_app(cred)
        
        self.db = firestore.client()
    
    def verify_token(self, id_token):
        """التحقق من رمز Firebase"""
        try:
            decoded_token = auth.verify_id_token(id_token)
            return decoded_token
        except Exception as e:
            print(f"خطأ في التحقق من الرمز: {e}")
            return None
    
    def create_user(self, email, password, display_name=None):
        """إنشاء مستخدم جديد في Firebase"""
        try:
            user = auth.create_user(
                email=email,
                password=password,
                display_name=display_name
            )
            return user
        except Exception as e:
            print(f"خطأ في إنشاء المستخدم: {e}")
            return None
    
    def get_user_by_email(self, email):
        """الحصول على مستخدم بالبريد الإلكتروني"""
        try:
            user = auth.get_user_by_email(email)
            return user
        except Exception as e:
            print(f"خطأ في الحصول على المستخدم: {e}")
            return None
    
    def update_user(self, uid, **kwargs):
        """تحديث بيانات المستخدم"""
        try:
            user = auth.update_user(uid, **kwargs)
            return user
        except Exception as e:
            print(f"خطأ في تحديث المستخدم: {e}")
            return None
    
    def delete_user(self, uid):
        """حذف مستخدم"""
        try:
            auth.delete_user(uid)
            return True
        except Exception as e:
            print(f"خطأ في حذف المستخدم: {e}")
            return False
    
    # Firestore operations
    def add_document(self, collection, data):
        """إضافة مستند جديد"""
        try:
            doc_ref = self.db.collection(collection).add(data)
            return doc_ref[1].id
        except Exception as e:
            print(f"خطأ في إضافة المستند: {e}")
            return None
    
    def get_document(self, collection, doc_id):
        """الحصول على مستند"""
        try:
            doc = self.db.collection(collection).document(doc_id).get()
            if doc.exists:
                return doc.to_dict()
            return None
        except Exception as e:
            print(f"خطأ في الحصول على المستند: {e}")
            return None
    
    def update_document(self, collection, doc_id, data):
        """تحديث مستند"""
        try:
            self.db.collection(collection).document(doc_id).update(data)
            return True
        except Exception as e:
            print(f"خطأ في تحديث المستند: {e}")
            return False
    
    def delete_document(self, collection, doc_id):
        """حذف مستند"""
        try:
            self.db.collection(collection).document(doc_id).delete()
            return True
        except Exception as e:
            print(f"خطأ في حذف المستند: {e}")
            return False
    
    def get_collection(self, collection, filters=None, order_by=None, limit=None):
        """الحصول على مجموعة من المستندات"""
        try:
            query = self.db.collection(collection)
            
            if filters:
                for field, operator, value in filters:
                    query = query.where(field, operator, value)
            
            if order_by:
                query = query.order_by(order_by)
            
            if limit:
                query = query.limit(limit)
            
            docs = query.stream()
            return [{'id': doc.id, **doc.to_dict()} for doc in docs]
        except Exception as e:
            print(f"خطأ في الحصول على المجموعة: {e}")
            return []


# إنشاء مثيل واحد من الخدمة
firebase_service = FirebaseService()
