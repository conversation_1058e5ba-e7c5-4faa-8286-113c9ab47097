{% extends 'base.html' %}
{% load static %}

{% block title %}قائمة الأعضاء - نظام إدارة التضامن{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="page-title">
                <i class="fas fa-users me-3"></i>
                قائمة الأعضاء
            </h1>
            <a href="{% url 'member_create' %}" class="btn btn-primary">
                <i class="fas fa-user-plus me-2"></i>
                إضافة عضو جديد
            </a>
        </div>
    </div>
</div>

<!-- فلاتر البحث -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <form method="get" class="row g-3">
                    <div class="col-md-4">
                        <input type="text" class="form-control" name="search" 
                               value="{{ search }}" placeholder="البحث بالاسم أو الهاتف أو المدينة">
                    </div>
                    <div class="col-md-3">
                        <select name="status" class="form-control">
                            <option value="">جميع الحالات</option>
                            <option value="active" {% if status == 'active' %}selected{% endif %}>نشط</option>
                            <option value="inactive" {% if status == 'inactive' %}selected{% endif %}>غير نشط</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <button type="submit" class="btn btn-outline-primary">
                            <i class="fas fa-search me-2"></i>
                            بحث
                        </button>
                        <a href="{% url 'member_list' %}" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-2"></i>
                            إلغاء
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-list me-2"></i>
                    الأعضاء المسجلين ({{ members.count }})
                </h5>
            </div>
            <div class="card-body">
                {% if members %}
                <div class="table-responsive">
                    <table class="table table-hover" id="membersTable">
                        <thead>
                            <tr>
                                <th>الاسم</th>
                                <th>الهاتف</th>
                                <th>المدينة</th>
                                <th>آخر دفعة</th>
                                <th>إجمالي الدفعات</th>
                                <th>الحالة</th>
                                <th>تاريخ التسجيل</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for member in members %}
                            <tr>
                                <td>
                                    <strong>{{ member.name }}</strong>
                                    {% if member.notes %}
                                    <br><small class="text-muted">{{ member.notes|truncatechars:30 }}</small>
                                    {% endif %}
                                </td>
                                <td>
                                    <a href="tel:{{ member.phone }}">{{ member.phone }}</a>
                                </td>
                                <td>{{ member.city }}</td>
                                <td>
                                    {% with last_payment=member.get_last_payment %}
                                        {% if last_payment %}
                                            {{ last_payment.payment_date|date:"Y/m/d" }}
                                            <br><small class="text-muted">{{ last_payment.amount }} ر.س</small>
                                        {% else %}
                                            <span class="text-muted">لا توجد دفعات</span>
                                        {% endif %}
                                    {% endwith %}
                                </td>
                                <td>
                                    <span class="fw-bold text-success">{{ member.get_total_payments|floatformat:2 }} ر.س</span>
                                </td>
                                <td>
                                    {% if member.is_active %}
                                        <span class="badge bg-success">نشط</span>
                                    {% else %}
                                        <span class="badge bg-danger">غير نشط</span>
                                    {% endif %}
                                </td>
                                <td>{{ member.created_at|date:"Y/m/d" }}</td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="#" class="btn btn-sm btn-outline-info" title="عرض التفاصيل">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="#" class="btn btn-sm btn-outline-warning" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="{% url 'payment_create' %}"
                                           class="btn btn-sm btn-outline-success" title="إضافة دفعة">
                                            <i class="fas fa-money-bill"></i>
                                        </a>
                                        <button type="button" class="btn btn-sm btn-outline-danger" 
                                                title="حذف" 
                                                data-confirm-delete="هل أنت متأكد من حذف العضو '{{ member.name }}'؟">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                
                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-users fa-3x text-muted mb-3"></i>
                    <h4 class="text-muted">لا توجد أعضاء مسجلين</h4>
                    <p class="text-muted">ابدأ بإضافة عضو جديد للمؤسسة</p>
                    <a href="{% url 'member_create' %}" class="btn btn-primary">
                        <i class="fas fa-user-plus me-2"></i>
                        إضافة عضو جديد
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- إحصائيات سريعة -->
{% if members %}
<div class="row mt-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-primary text-white">
            <div class="card-body text-center">
                <i class="fas fa-users fa-2x mb-2"></i>
                <h4>{{ members.count }}</h4>
                <p class="mb-0">إجمالي الأعضاء</p>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-success text-white">
            <div class="card-body text-center">
                <i class="fas fa-check-circle fa-2x mb-2"></i>
                <h4>{{ members|length }}</h4>
                <p class="mb-0">الأعضاء النشطون</p>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-warning text-white">
            <div class="card-body text-center">
                <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                <h4>0</h4>
                <p class="mb-0">الأعضاء المتأخرون</p>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-info text-white">
            <div class="card-body text-center">
                <i class="fas fa-money-bill-wave fa-2x mb-2"></i>
                <h4>0 ر.س</h4>
                <p class="mb-0">إجمالي الدفعات</p>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
    // تفعيل البحث في الجدول
    document.addEventListener('DOMContentLoaded', function() {
        // إضافة وظائف إضافية هنا
    });
</script>
{% endblock %}
