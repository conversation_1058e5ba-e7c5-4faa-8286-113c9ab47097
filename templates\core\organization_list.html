{% extends 'base.html' %}
{% load static %}

{% block title %}قائمة المؤسسات - نظام إدارة التضامن{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="page-title">
                <i class="fas fa-building me-3"></i>
                قائمة المؤسسات
            </h1>
            <a href="{% url 'organization_create' %}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>
                إضافة مؤسسة جديدة
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-list me-2"></i>
                    المؤسسات المسجلة ({{ organizations.count }})
                </h5>
            </div>
            <div class="card-body">
                {% if organizations %}
                <div class="table-responsive">
                    <table class="table table-hover" id="organizationsTable">
                        <thead>
                            <tr>
                                <th>اسم المؤسسة</th>
                                <th>البريد الإلكتروني</th>
                                <th>الهاتف</th>
                                <th>عدد الأعضاء</th>
                                <th>الرصيد</th>
                                <th>الحالة</th>
                                <th>تاريخ الإنشاء</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for org in organizations %}
                            <tr>
                                <td>
                                    <strong>{{ org.name }}</strong>
                                    {% if org.description %}
                                    <br><small class="text-muted">{{ org.description|truncatechars:50 }}</small>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if org.email %}
                                        <a href="mailto:{{ org.email }}">{{ org.email }}</a>
                                    {% else %}
                                        <span class="text-muted">غير محدد</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if org.phone %}
                                        <a href="tel:{{ org.phone }}">{{ org.phone }}</a>
                                    {% else %}
                                        <span class="text-muted">غير محدد</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <span class="badge bg-info">{{ org.get_total_members }}</span>
                                </td>
                                <td>
                                    <span class="fw-bold {% if org.get_total_balance >= 0 %}text-success{% else %}text-danger{% endif %}">
                                        {{ org.get_total_balance|floatformat:2 }} ر.س
                                    </span>
                                </td>
                                <td>
                                    {% if org.is_active %}
                                        <span class="badge bg-success">نشط</span>
                                    {% else %}
                                        <span class="badge bg-danger">غير نشط</span>
                                    {% endif %}
                                </td>
                                <td>{{ org.created_at|date:"Y/m/d" }}</td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="#" class="btn btn-sm btn-outline-info" title="عرض التفاصيل">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="#" class="btn btn-sm btn-outline-warning" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button type="button" class="btn btn-sm btn-outline-danger" 
                                                title="حذف" 
                                                data-confirm-delete="هل أنت متأكد من حذف المؤسسة '{{ org.name }}'؟">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                
                <!-- Search Box -->
                <div class="row mt-3">
                    <div class="col-md-6">
                        <input type="text" class="form-control" id="searchInput" 
                               placeholder="البحث في المؤسسات..." 
                               data-search-table="organizationsTable">
                    </div>
                </div>
                
                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-building fa-3x text-muted mb-3"></i>
                    <h4 class="text-muted">لا توجد مؤسسات مسجلة</h4>
                    <p class="text-muted">ابدأ بإضافة مؤسسة جديدة للنظام</p>
                    <a href="{% url 'organization_create' %}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>
                        إضافة مؤسسة جديدة
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- إحصائيات سريعة -->
{% if organizations %}
<div class="row mt-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-primary text-white">
            <div class="card-body text-center">
                <i class="fas fa-building fa-2x mb-2"></i>
                <h4>{{ organizations.count }}</h4>
                <p class="mb-0">إجمالي المؤسسات</p>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-success text-white">
            <div class="card-body text-center">
                <i class="fas fa-check-circle fa-2x mb-2"></i>
                <h4>{{ organizations|length }}</h4>
                <p class="mb-0">المؤسسات النشطة</p>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-info text-white">
            <div class="card-body text-center">
                <i class="fas fa-users fa-2x mb-2"></i>
                <h4>0</h4>
                <p class="mb-0">إجمالي الأعضاء</p>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-warning text-white">
            <div class="card-body text-center">
                <i class="fas fa-wallet fa-2x mb-2"></i>
                <h4>0 ر.س</h4>
                <p class="mb-0">إجمالي الأرصدة</p>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
    // تفعيل البحث في الجدول
    document.addEventListener('DOMContentLoaded', function() {
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            searchTable('searchInput', 'organizationsTable');
        }
    });
</script>
{% endblock %}
