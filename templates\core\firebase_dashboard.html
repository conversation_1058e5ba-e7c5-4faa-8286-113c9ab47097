{% extends 'base.html' %}
{% load static %}

{% block title %}لوحة تحكم Firebase - نظام إدارة التضامن{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="page-title">
            <i class="fab fa-google me-3"></i>
            لوحة تحكم Firebase
        </h1>
        <p class="text-muted mb-4">
            عرض البيانات المتزامنة مع Firebase Firestore
        </p>
    </div>
</div>

{% if firebase_connected %}
<!-- حالة الاتصال -->
<div class="row mb-4">
    <div class="col-12">
        <div class="alert alert-success">
            <i class="fas fa-check-circle me-2"></i>
            <strong>متصل بـ Firebase!</strong>
            Project ID: tadamon-4a9bb
        </div>
    </div>
</div>

<!-- إحصائيات Firebase -->
<div class="row mb-4">
    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
        <div class="card bg-primary text-white">
            <div class="card-body text-center">
                <i class="fas fa-users fa-2x mb-2"></i>
                <h4>{{ firebase_stats.users_count }}</h4>
                <p class="mb-0">المستخدمين</p>
            </div>
        </div>
    </div>
    
    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
        <div class="card bg-success text-white">
            <div class="card-body text-center">
                <i class="fas fa-building fa-2x mb-2"></i>
                <h4>{{ firebase_stats.organizations_count }}</h4>
                <p class="mb-0">المؤسسات</p>
            </div>
        </div>
    </div>
    
    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
        <div class="card bg-info text-white">
            <div class="card-body text-center">
                <i class="fas fa-user-friends fa-2x mb-2"></i>
                <h4>{{ firebase_stats.members_count }}</h4>
                <p class="mb-0">الأعضاء</p>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 col-sm-6 mb-3">
        <div class="card bg-warning text-white">
            <div class="card-body text-center">
                <i class="fas fa-money-bill-wave fa-2x mb-2"></i>
                <h4>{{ firebase_stats.payments_count }}</h4>
                <p class="mb-0">الدفعات</p>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 col-sm-6 mb-3">
        <div class="card bg-danger text-white">
            <div class="card-body text-center">
                <i class="fas fa-receipt fa-2x mb-2"></i>
                <h4>{{ firebase_stats.expenses_count }}</h4>
                <p class="mb-0">المصاريف</p>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- آخر الدفعات من Firebase -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-money-bill-wave me-2"></i>
                    آخر الدفعات (من Firebase)
                </h5>
            </div>
            <div class="card-body">
                {% if recent_payments %}
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>العضو</th>
                                <th>المبلغ</th>
                                <th>التاريخ</th>
                                <th>المؤسسة</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for payment in recent_payments %}
                            <tr>
                                <td>{{ payment.member_name }}</td>
                                <td>
                                    <span class="text-success fw-bold">{{ payment.amount }} ر.س</span>
                                </td>
                                <td>{{ payment.payment_date }}</td>
                                <td>
                                    <small class="text-muted">{{ payment.organization_name }}</small>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-3">
                    <i class="fas fa-inbox text-muted fa-2x mb-2"></i>
                    <p class="text-muted">لا توجد دفعات في Firebase</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <!-- آخر المصاريف من Firebase -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-receipt me-2"></i>
                    آخر المصاريف (من Firebase)
                </h5>
            </div>
            <div class="card-body">
                {% if recent_expenses %}
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>النوع</th>
                                <th>المبلغ</th>
                                <th>التاريخ</th>
                                <th>المؤسسة</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for expense in recent_expenses %}
                            <tr>
                                <td>{{ expense.expense_type }}</td>
                                <td>
                                    <span class="text-danger fw-bold">{{ expense.amount }} ر.س</span>
                                </td>
                                <td>{{ expense.expense_date }}</td>
                                <td>
                                    <small class="text-muted">{{ expense.organization_name }}</small>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-3">
                    <i class="fas fa-inbox text-muted fa-2x mb-2"></i>
                    <p class="text-muted">لا توجد مصاريف في Firebase</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- أدوات Firebase -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-tools me-2"></i>
                    أدوات Firebase
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <button type="button" class="btn btn-primary w-100" onclick="syncFirebase()">
                            <i class="fas fa-sync me-2"></i>
                            مزامنة البيانات
                        </button>
                    </div>
                    <div class="col-md-3 mb-3">
                        <button type="button" class="btn btn-info w-100" onclick="testFirebase()">
                            <i class="fas fa-vial me-2"></i>
                            اختبار الاتصال
                        </button>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="https://console.firebase.google.com/project/tadamon-4a9bb/firestore" 
                           target="_blank" class="btn btn-warning w-100">
                            <i class="fas fa-external-link-alt me-2"></i>
                            فتح Firebase Console
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <button type="button" class="btn btn-success w-100" onclick="location.reload()">
                            <i class="fas fa-refresh me-2"></i>
                            تحديث الصفحة
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% else %}
<!-- خطأ في الاتصال -->
<div class="row">
    <div class="col-12">
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <strong>خطأ في الاتصال بـ Firebase!</strong>
            <br>{{ error_message }}
        </div>
        
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">خطوات حل المشكلة</h5>
            </div>
            <div class="card-body">
                <ol>
                    <li>تأكد من صحة معلومات Firebase في ملف .env</li>
                    <li>تأكد من تفعيل Firestore في مشروع Firebase</li>
                    <li>تأكد من صحة صلاحيات Service Account</li>
                    <li>تأكد من اتصال الإنترنت</li>
                </ol>
                
                <div class="mt-3">
                    <button type="button" class="btn btn-primary" onclick="testFirebase()">
                        <i class="fas fa-vial me-2"></i>
                        اختبار الاتصال مرة أخرى
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
    function syncFirebase() {
        if (confirm('هل تريد مزامنة جميع البيانات مع Firebase؟')) {
            showSuccessMessage('جاري مزامنة البيانات...');
            // يمكن إضافة AJAX call هنا لتشغيل أمر المزامنة
        }
    }
    
    function testFirebase() {
        showSuccessMessage('جاري اختبار الاتصال...');
        // يمكن إضافة AJAX call هنا لاختبار الاتصال
    }
</script>
{% endblock %}
