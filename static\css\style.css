/* تنسيق عام */
body {
    font-family: 'Cairo', sans-serif;
    background-color: #f8f9fa;
    line-height: 1.6;
}

/* تنسيق الروابط */
a {
    text-decoration: none;
    transition: all 0.3s ease;
}

a:hover {
    text-decoration: none;
}

/* تنسيق البطاقات */
.card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.2);
}

.card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px 15px 0 0 !important;
    border: none;
    font-weight: 600;
}

/* تنسيق الأزرار */
.btn {
    border-radius: 25px;
    font-weight: 500;
    padding: 10px 20px;
    transition: all 0.3s ease;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    transform: translateY(-2px);
}

.btn-success {
    background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
    border: none;
}

.btn-success:hover {
    background: linear-gradient(135deg, #0e8478 0%, #2dd865 100%);
}

.btn-danger {
    background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);
    border: none;
}

.btn-danger:hover {
    background: linear-gradient(135deg, #e63946 0%, #e63946 100%);
}

.btn-warning {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    border: none;
    color: white;
}

.btn-info {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    border: none;
}

/* تنسيق النماذج */
.form-control {
    border-radius: 10px;
    border: 2px solid #e9ecef;
    padding: 12px 15px;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 8px;
}

/* تنسيق الجداول */
.table {
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.table thead th {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    font-weight: 600;
    padding: 15px;
}

.table tbody tr {
    transition: background-color 0.3s ease;
}

.table tbody tr:hover {
    background-color: #f8f9fa;
}

.table tbody td {
    padding: 12px 15px;
    vertical-align: middle;
}

/* تنسيق الإحصائيات */
.stats-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 20px;
    padding: 25px;
    text-align: center;
    transition: transform 0.3s ease;
}

.stats-card:hover {
    transform: scale(1.05);
}

.stats-card .stats-icon {
    font-size: 3rem;
    margin-bottom: 15px;
    opacity: 0.8;
}

.stats-card .stats-number {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 5px;
}

.stats-card .stats-label {
    font-size: 1.1rem;
    opacity: 0.9;
}

/* تنسيق حالة العضو */
.member-status {
    padding: 5px 15px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
}

.status-active {
    background-color: #d4edda;
    color: #155724;
}

.status-warning {
    background-color: #fff3cd;
    color: #856404;
}

.status-danger {
    background-color: #f8d7da;
    color: #721c24;
}

/* تنسيق صفحة تسجيل الدخول */
.login-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.login-card {
    background: white;
    border-radius: 20px;
    padding: 40px;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    width: 100%;
    max-width: 400px;
}

.login-logo {
    text-align: center;
    margin-bottom: 30px;
}

.login-logo i {
    font-size: 4rem;
    color: #667eea;
    margin-bottom: 15px;
}

.login-logo h2 {
    color: #333;
    font-weight: 700;
}

/* تنسيق الرسائل */
.alert {
    border-radius: 15px;
    border: none;
    padding: 15px 20px;
    font-weight: 500;
}

.alert-success {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    color: #155724;
}

.alert-danger {
    background: linear-gradient(135deg, #f8d7da 0%, #f1b0b7 100%);
    color: #721c24;
}

.alert-warning {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    color: #856404;
}

.alert-info {
    background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
    color: #0c5460;
}

/* تنسيق التنقل */
.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
}

.nav-link {
    font-weight: 500;
    transition: color 0.3s ease;
}

.nav-link:hover {
    color: #fff !important;
}

/* تنسيق الفوتر */
footer {
    margin-top: auto;
}

/* تنسيق متجاوب */
@media (max-width: 768px) {
    .stats-card {
        margin-bottom: 20px;
    }
    
    .table-responsive {
        border-radius: 15px;
    }
    
    .login-card {
        margin: 20px;
        padding: 30px 20px;
    }
}

/* تنسيق الرسوم البيانية */
.chart-container {
    position: relative;
    height: 400px;
    margin: 20px 0;
}

/* تنسيق الأيقونات */
.icon-circle {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    margin-left: 15px;
}

.icon-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.icon-success {
    background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
}

.icon-warning {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.icon-danger {
    background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);
}

/* تحسينات إضافية */
.page-title {
    color: #333;
    font-weight: 700;
    margin-bottom: 30px;
    position: relative;
}

.page-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    right: 0;
    width: 50px;
    height: 3px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 2px;
}

.section-divider {
    height: 2px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    border-radius: 1px;
    margin: 30px 0;
}
