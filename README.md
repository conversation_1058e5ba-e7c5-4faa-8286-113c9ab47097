# نظام إدارة التضامن

نظام ويب شامل لإدارة الصناديق والجمعيات الخيرية مبني بـ Django و Firebase.

## 🎯 وصف المشروع

نظام إدارة التضامن هو منصة ويب تساعد مالكي المشاريع على إدارة النظام بالكامل، وتمكّن مدراء الصناديق أو الجمعيات من تسجيل الدخول إلى لوحة تحكم خاصة بصندوقهم، لإدارة الأعضاء والدفعات والمصاريف بسلاسة وبدون أي تعقيد.

## 👥 أنواع المستخدمين

### 1. مالك المشروع (Super Admin)
- تسجيل وإنشاء مدراء جدد
- رؤية كل الصناديق / الجمعيات في النظام
- إدارة الاشتراكات المدفوعة لكل صندوق
- التحكم في صلاحيات المدراء
- رؤية تقارير شاملة لكل النظام

### 2. مدير صندوق / جمعية (Manager)
- تسجيل الأعضاء التابعين له فقط
- إضافة دفعات ومتابعة الاشتراكات
- تسجيل مصاريف الجمعية أو الصندوق
- رؤية التقارير الشهرية والرصيد الحالي
- لا يمكنه رؤية أو تعديل صناديق الآخرين

## 🛠️ التقنيات المستخدمة

- **Backend**: Django 5.1.2
- **Frontend**: HTML5, CSS3, JavaScript, Bootstrap 5 (RTL)
- **Database**: SQLite (للتطوير) / Firebase (للإنتاج)
- **Authentication**: Django Auth + Firebase Auth
- **Styling**: Bootstrap 5 RTL, Font Awesome, Google Fonts (Cairo)
- **Charts**: Chart.js
- **Export**: openpyxl, reportlab

## 📋 المتطلبات

- Python 3.11+
- Django 5.1.2
- firebase-admin
- python-decouple
- django-cors-headers
- openpyxl
- reportlab

## 🚀 التثبيت والتشغيل

### 1. استنساخ المشروع
```bash
git clone <repository-url>
cd Tadamon
```

### 2. إنشاء البيئة الافتراضية
```bash
python -m venv tadamon_env
tadamon_env\Scripts\activate  # Windows
# أو
source tadamon_env/bin/activate  # Linux/Mac
```

### 3. تثبيت المتطلبات
```bash
pip install django firebase-admin python-decouple django-cors-headers openpyxl reportlab
```

### 4. تكوين قاعدة البيانات
```bash
python manage.py makemigrations
python manage.py migrate
```

### 5. إنشاء مستخدم مدير
```bash
python manage.py createsuperuser
```

### 6. إنشاء بيانات تجريبية (اختياري)
```bash
python manage.py create_sample_data
```

### 7. تشغيل الخادم
```bash
python manage.py runserver
```

الآن يمكنك الوصول للنظام عبر: http://127.0.0.1:8000

## 🔐 بيانات الدخول الافتراضية

### مالك المشروع (Super Admin)
- **اسم المستخدم**: admin
- **كلمة المرور**: admin123

### مدراء المؤسسات (تم إنشاؤها بالبيانات التجريبية)
- **المدير 1**: manager1 / manager123
- **المدير 2**: manager2 / manager123  
- **المدير 3**: manager3 / manager123

## 📁 هيكل المشروع

```
Tadamon/
├── core/                          # التطبيق الرئيسي
│   ├── models.py                  # نماذج البيانات
│   ├── views.py                   # المعالجات
│   ├── forms.py                   # النماذج
│   ├── urls.py                    # المسارات
│   ├── admin.py                   # لوحة الإدارة
│   ├── utils.py                   # الوظائف المساعدة
│   ├── firebase_config.py         # تكوين Firebase
│   └── management/commands/       # أوامر إدارية مخصصة
├── templates/                     # القوالب
│   ├── base.html                  # القالب الأساسي
│   └── core/                      # قوالب التطبيق
├── static/                        # الملفات الثابتة
│   ├── css/                       # ملفات CSS
│   ├── js/                        # ملفات JavaScript
│   └── images/                    # الصور
├── tadamon_system/                # إعدادات المشروع
│   ├── settings.py                # الإعدادات
│   └── urls.py                    # المسارات الرئيسية
└── manage.py                      # أداة إدارة Django
```

## 🎨 المميزات

### ✅ المميزات المكتملة
- نظام تسجيل الدخول والمصادقة
- لوحة تحكم تفاعلية مع إحصائيات
- إدارة المؤسسات (للمدير العام)
- إدارة مدراء المؤسسات
- إدارة الأعضاء
- تسجيل الدفعات
- تسجيل المصاريف
- تصميم متجاوب (RTL) باللغة العربية
- نظام صلاحيات متقدم
- تسجيل الأنشطة
- واجهة مستخدم احترافية

### 🔄 قيد التطوير
- التقارير والإحصائيات المتقدمة
- تصدير البيانات (Excel/PDF)
- نظام الإشعارات
- تكامل Firebase الكامل
- نظام الاشتراكات المدفوعة
- API للتطبيقات المحمولة

## 🔧 التكوين

### متغيرات البيئة (.env)
```env
SECRET_KEY=your-secret-key-here
DEBUG=True
ALLOWED_HOSTS=localhost,127.0.0.1

# Firebase Configuration
FIREBASE_TYPE=service_account
FIREBASE_PROJECT_ID=your-project-id
FIREBASE_PRIVATE_KEY_ID=your-private-key-id
FIREBASE_PRIVATE_KEY=your-private-key
FIREBASE_CLIENT_EMAIL=your-client-email
FIREBASE_CLIENT_ID=your-client-id
```

## 📱 الاستخدام

### للمدير العام (Super Admin)
1. تسجيل الدخول بحساب المدير العام
2. إنشاء مؤسسات جديدة
3. إضافة مدراء للمؤسسات
4. مراقبة الإحصائيات العامة
5. إدارة الاشتراكات

### لمدير المؤسسة (Manager)
1. تسجيل الدخول بحساب المدير
2. إضافة أعضاء جدد
3. تسجيل دفعات الأعضاء
4. تسجيل مصاريف المؤسسة
5. مراجعة التقارير والإحصائيات

## 🤝 المساهمة

نرحب بالمساهمات! يرجى اتباع الخطوات التالية:

1. Fork المشروع
2. إنشاء فرع للميزة الجديدة (`git checkout -b feature/AmazingFeature`)
3. Commit التغييرات (`git commit -m 'Add some AmazingFeature'`)
4. Push للفرع (`git push origin feature/AmazingFeature`)
5. فتح Pull Request

## 📄 الرخصة

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## 📞 التواصل

- **المطور**: فريق تطوير نظام التضامن
- **البريد الإلكتروني**: <EMAIL>
- **الموقع**: https://tadamon-system.com

## 🙏 شكر وتقدير

- Bootstrap Team لإطار العمل الرائع
- Font Awesome للأيقونات
- Google Fonts للخطوط العربية
- Django Community للإطار المتميز
- Firebase Team لخدمات قاعدة البيانات

---

**نظام إدارة التضامن** - حلول تقنية لإدارة الجمعيات والصناديق الخيرية 💙
