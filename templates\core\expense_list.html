{% extends 'base.html' %}
{% load static %}

{% block title %}قائمة المصاريف - نظام إدارة التضامن{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="page-title">
                <i class="fas fa-receipt me-3"></i>
                قائمة المصاريف
            </h1>
            <a href="{% url 'expense_create' %}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>
                تسجيل مصروف جديد
            </a>
        </div>
    </div>
</div>

<!-- فلاتر البحث -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <form method="get" class="row g-3">
                    <div class="col-md-3">
                        <label class="form-label">من تاريخ</label>
                        <input type="date" class="form-control" name="start_date" value="{{ start_date }}">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">إلى تاريخ</label>
                        <input type="date" class="form-control" name="end_date" value="{{ end_date }}">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">نوع المصروف</label>
                        <select name="expense_type" class="form-control">
                            <option value="">جميع الأنواع</option>
                            <option value="operational" {% if expense_type == 'operational' %}selected{% endif %}>مصاريف تشغيلية</option>
                            <option value="maintenance" {% if expense_type == 'maintenance' %}selected{% endif %}>صيانة</option>
                            <option value="utilities" {% if expense_type == 'utilities' %}selected{% endif %}>خدمات</option>
                            <option value="salaries" {% if expense_type == 'salaries' %}selected{% endif %}>رواتب</option>
                            <option value="other" {% if expense_type == 'other' %}selected{% endif %}>أخرى</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">&nbsp;</label>
                        <div>
                            <button type="submit" class="btn btn-outline-primary">
                                <i class="fas fa-search me-2"></i>
                                بحث
                            </button>
                            <a href="{% url 'expense_list' %}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-2"></i>
                                إلغاء
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-list me-2"></i>
                    المصاريف المسجلة ({{ expenses.count }})
                </h5>
            </div>
            <div class="card-body">
                {% if expenses %}
                <div class="table-responsive">
                    <table class="table table-hover" id="expensesTable">
                        <thead>
                            <tr>
                                <th>نوع المصروف</th>
                                <th>المبلغ</th>
                                <th>المستفيد</th>
                                <th>الوصف</th>
                                <th>تاريخ المصروف</th>
                                <th>المسجل بواسطة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for expense in expenses %}
                            <tr>
                                <td>
                                    <span class="badge bg-warning">{{ expense.get_expense_type_display }}</span>
                                </td>
                                <td>
                                    <span class="fw-bold text-danger">{{ expense.amount|floatformat:2 }} ر.س</span>
                                </td>
                                <td>{{ expense.beneficiary }}</td>
                                <td>
                                    {{ expense.description|truncatechars:50 }}
                                    {% if expense.notes %}
                                    <br><small class="text-muted">{{ expense.notes|truncatechars:30 }}</small>
                                    {% endif %}
                                </td>
                                <td>{{ expense.expense_date|date:"Y/m/d" }}</td>
                                <td>
                                    {{ expense.created_by.get_full_name|default:expense.created_by.username }}
                                    <br><small class="text-muted">{{ expense.created_at|date:"Y/m/d H:i" }}</small>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="#" class="btn btn-sm btn-outline-info" title="عرض التفاصيل">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="#" class="btn btn-sm btn-outline-warning" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button type="button" class="btn btn-sm btn-outline-danger" 
                                                title="حذف" 
                                                data-confirm-delete="هل أنت متأكد من حذف المصروف؟">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                
                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-receipt fa-3x text-muted mb-3"></i>
                    <h4 class="text-muted">لا توجد مصاريف مسجلة</h4>
                    <p class="text-muted">ابدأ بتسجيل مصروف جديد</p>
                    <a href="{% url 'expense_create' %}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>
                        تسجيل مصروف جديد
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- إحصائيات سريعة -->
{% if expenses %}
<div class="row mt-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-danger text-white">
            <div class="card-body text-center">
                <i class="fas fa-list fa-2x mb-2"></i>
                <h4>{{ expenses.count }}</h4>
                <p class="mb-0">إجمالي المصاريف</p>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-warning text-white">
            <div class="card-body text-center">
                <i class="fas fa-money-bill fa-2x mb-2"></i>
                <h4>0 ر.س</h4>
                <p class="mb-0">إجمالي المبالغ</p>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-info text-white">
            <div class="card-body text-center">
                <i class="fas fa-calendar fa-2x mb-2"></i>
                <h4>0</h4>
                <p class="mb-0">مصاريف هذا الشهر</p>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-secondary text-white">
            <div class="card-body text-center">
                <i class="fas fa-chart-line fa-2x mb-2"></i>
                <h4>0 ر.س</h4>
                <p class="mb-0">متوسط المصروف</p>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // إضافة وظائف إضافية هنا
    });
</script>
{% endblock %}
