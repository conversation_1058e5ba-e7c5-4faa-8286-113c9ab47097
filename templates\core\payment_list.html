{% extends 'base.html' %}
{% load static %}

{% block title %}قائمة الدفعات - نظام إدارة التضامن{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="page-title">
                <i class="fas fa-money-bill-wave me-3"></i>
                قائمة الدفعات
            </h1>
            <a href="{% url 'payment_create' %}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>
                تسجيل دفعة جديدة
            </a>
        </div>
    </div>
</div>

<!-- فلاتر البحث -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <form method="get" class="row g-3">
                    <div class="col-md-3">
                        <label class="form-label">من تاريخ</label>
                        <input type="date" class="form-control" name="start_date" value="{{ start_date }}">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">إلى تاريخ</label>
                        <input type="date" class="form-control" name="end_date" value="{{ end_date }}">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">نوع الدفعة</label>
                        <select name="payment_type" class="form-control">
                            <option value="">جميع الأنواع</option>
                            <option value="monthly" {% if payment_type == 'monthly' %}selected{% endif %}>اشتراك شهري</option>
                            <option value="annual" {% if payment_type == 'annual' %}selected{% endif %}>اشتراك سنوي</option>
                            <option value="donation" {% if payment_type == 'donation' %}selected{% endif %}>تبرع</option>
                            <option value="other" {% if payment_type == 'other' %}selected{% endif %}>أخرى</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">&nbsp;</label>
                        <div>
                            <button type="submit" class="btn btn-outline-primary">
                                <i class="fas fa-search me-2"></i>
                                بحث
                            </button>
                            <a href="{% url 'payment_list' %}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-2"></i>
                                إلغاء
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-list me-2"></i>
                    الدفعات المسجلة ({{ payments.count }})
                </h5>
            </div>
            <div class="card-body">
                {% if payments %}
                <div class="table-responsive">
                    <table class="table table-hover" id="paymentsTable">
                        <thead>
                            <tr>
                                <th>العضو</th>
                                <th>المبلغ</th>
                                <th>نوع الدفعة</th>
                                <th>طريقة الدفع</th>
                                <th>تاريخ الدفع</th>
                                <th>المسجل بواسطة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for payment in payments %}
                            <tr>
                                <td>
                                    <strong>{{ payment.member.name }}</strong>
                                    <br><small class="text-muted">{{ payment.member.phone }}</small>
                                </td>
                                <td>
                                    <span class="fw-bold text-success">{{ payment.amount|floatformat:2 }} ر.س</span>
                                </td>
                                <td>
                                    <span class="badge bg-info">{{ payment.get_payment_type_display }}</span>
                                </td>
                                <td>{{ payment.get_payment_method_display }}</td>
                                <td>{{ payment.payment_date|date:"Y/m/d" }}</td>
                                <td>
                                    {{ payment.created_by.get_full_name|default:payment.created_by.username }}
                                    <br><small class="text-muted">{{ payment.created_at|date:"Y/m/d H:i" }}</small>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="#" class="btn btn-sm btn-outline-info" title="عرض التفاصيل">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="#" class="btn btn-sm btn-outline-warning" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button type="button" class="btn btn-sm btn-outline-danger" 
                                                title="حذف" 
                                                data-confirm-delete="هل أنت متأكد من حذف الدفعة؟">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                
                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-money-bill-wave fa-3x text-muted mb-3"></i>
                    <h4 class="text-muted">لا توجد دفعات مسجلة</h4>
                    <p class="text-muted">ابدأ بتسجيل دفعة جديدة</p>
                    <a href="{% url 'payment_create' %}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>
                        تسجيل دفعة جديدة
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- إحصائيات سريعة -->
{% if payments %}
<div class="row mt-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-primary text-white">
            <div class="card-body text-center">
                <i class="fas fa-list fa-2x mb-2"></i>
                <h4>{{ payments.count }}</h4>
                <p class="mb-0">إجمالي الدفعات</p>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-success text-white">
            <div class="card-body text-center">
                <i class="fas fa-money-bill-wave fa-2x mb-2"></i>
                <h4>0 ر.س</h4>
                <p class="mb-0">إجمالي المبالغ</p>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-info text-white">
            <div class="card-body text-center">
                <i class="fas fa-calendar fa-2x mb-2"></i>
                <h4>0</h4>
                <p class="mb-0">دفعات هذا الشهر</p>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-warning text-white">
            <div class="card-body text-center">
                <i class="fas fa-chart-line fa-2x mb-2"></i>
                <h4>0 ر.س</h4>
                <p class="mb-0">متوسط الدفعة</p>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // إضافة وظائف إضافية هنا
    });
</script>
{% endblock %}
